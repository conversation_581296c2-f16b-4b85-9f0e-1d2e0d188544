"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import {
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  Menu as MenuIcon,
  ChevronRight,
  ChevronDown,
  GripVertical,
  Link as LinkIcon,
  FileText,
  Tag,
  Package,
  Minus,
  ExternalLink,
} from "lucide-react";
import { toast } from "sonner";
import { AdminDataTable } from "@/lib/admin/components/AdminDataTable";
import { useAdminData, useAdminCrud } from "@/lib/admin/hooks";

interface Menu {
  id: string;
  name: string;
  location: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  items: MenuItem[];
}

interface MenuItem {
  id: string;
  menuId: string;
  parentId?: string;
  title: string;
  url?: string;
  type: "LINK" | "PAGE" | "CATEGORY" | "PRODUCT" | "CUSTOM" | "SEPARATOR";
  target?: string;
  icon?: string;
  cssClass?: string;
  order: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  children?: MenuItem[];
}

const menuLocations = [
  { value: "header", label: "Header Navigation" },
  { value: "footer", label: "Footer Navigation" },
  { value: "sidebar", label: "Sidebar Navigation" },
  { value: "mobile", label: "Mobile Navigation" },
];

const menuItemTypes = [
  { value: "LINK", label: "Liên kết", icon: LinkIcon },
  { value: "PAGE", label: "Trang", icon: FileText },
  { value: "CATEGORY", label: "Danh mục", icon: Tag },
  { value: "PRODUCT", label: "Sản phẩm", icon: Package },
  { value: "CUSTOM", label: "Tùy chỉnh", icon: MenuIcon },
  { value: "SEPARATOR", label: "Phân cách", icon: Minus },
];

export default function AdminMenusPage() {
  const [selectedMenu, setSelectedMenu] = useState<Menu | null>(null);
  const [isCreateMenuOpen, setIsCreateMenuOpen] = useState(false);
  const [isCreateItemOpen, setIsCreateItemOpen] = useState(false);
  const [editingMenu, setEditingMenu] = useState<Menu | null>(null);
  const [editingItem, setEditingItem] = useState<MenuItem | null>(null);

  // Data fetching for menus
  const {
    data: menus,
    loading: menusLoading,
    error: menusError,
    refresh: refreshMenus,
    setParams: setMenuParams,
    params: menuParams,
  } = useAdminData<Menu>({
    endpoint: "/api/admin/menus",
    initialParams: { page: 1, limit: 20 },
  });

  // CRUD operations
  const {
    create: createMenu,
    update: updateMenu,
    remove: deleteMenu,
  } = useAdminCrud("/api/admin/menus");
  const {
    create: createMenuItem,
    update: updateMenuItem,
    remove: deleteMenuItem,
  } = useAdminCrud("/api/admin/menu-items");

  // Menu table configuration
  const menuTableConfig = {
    columns: [
      {
        key: "name",
        title: "Tên Menu",
        sortable: true,
        render: (menu: Menu) => (
          <div>
            <div className="font-medium">{menu?.name || "N/A"}</div>
            <div className="text-sm text-muted-foreground">
              {menu?.description || ""}
            </div>
          </div>
        ),
      },
      {
        key: "location",
        title: "Vị trí",
        render: (menu: Menu) => (
          <Badge variant="outline">
            {menuLocations.find((loc) => loc.value === menu?.location)?.label ||
              menu?.location ||
              "N/A"}
          </Badge>
        ),
      },
      {
        key: "items",
        title: "Số mục",
        render: (menu: Menu) => (
          <span className="text-sm">{menu?.items?.length || 0} mục</span>
        ),
      },
      {
        key: "isActive",
        title: "Trạng thái",
        render: (menu: Menu) => (
          <Badge variant={menu?.isActive ? "default" : "secondary"}>
            {menu?.isActive ? "Hoạt động" : "Tạm dừng"}
          </Badge>
        ),
      },
      {
        key: "createdAt",
        title: "Ngày tạo",
        sortable: true,
        render: (menu: Menu) =>
          menu?.createdAt
            ? new Date(menu.createdAt).toLocaleDateString("vi-VN")
            : "N/A",
      },
    ],
    rowKey: "id",
    actions: {
      enabled: true,
      items: [
        {
          key: "view",
          label: "Xem chi tiết",
          icon: Eye,
          onClick: (menu: Menu) => setSelectedMenu(menu),
        },
        {
          key: "edit",
          label: "Chỉnh sửa",
          icon: Edit,
          onClick: (menu: Menu) => setEditingMenu(menu),
        },
        {
          key: "delete",
          label: "Xóa",
          icon: Trash2,
          onClick: (menu: Menu) => handleDeleteMenu(menu),
          variant: "destructive" as const,
        },
      ],
    },
    selection: {
      enabled: true,
      type: "checkbox" as const,
    },
  };

  const handleDeleteMenu = async (menu: Menu) => {
    if (!confirm(`Bạn có chắc chắn muốn xóa menu "${menu.name}"?`)) {
      return;
    }

    const result = await deleteMenu(menu.id);
    if (result) {
      toast.success("Xóa menu thành công");
      refreshMenus();
    }
  };

  const handleToggleMenuStatus = async (menu: Menu) => {
    const result = await updateMenu(menu.id, { isActive: !menu.isActive });
    if (result) {
      toast.success(
        `${menu.isActive ? "Tạm dừng" : "Kích hoạt"} menu thành công`
      );
      refreshMenus();
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Quản lý Menu</h1>
          <p className="text-muted-foreground">
            Quản lý menu navigation và cấu trúc điều hướng website
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setIsCreateMenuOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Tạo Menu Mới
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <MenuIcon className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">
                  Tổng Menu
                </p>
                <p className="text-2xl font-bold">{menus?.length || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Eye className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">
                  Menu Hoạt động
                </p>
                <p className="text-2xl font-bold">
                  {menus?.filter((menu) => menu.isActive).length || 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <LinkIcon className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">
                  Tổng Mục Menu
                </p>
                <p className="text-2xl font-bold">
                  {menus?.reduce(
                    (total, menu) => total + (menu.items?.length || 0),
                    0
                  ) || 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Tag className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">
                  Vị trí Menu
                </p>
                <p className="text-2xl font-bold">
                  {new Set(menus?.map((menu) => menu.location)).size || 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Menus Table */}
      <Card>
        <CardHeader>
          <CardTitle>Danh sách Menu</CardTitle>
        </CardHeader>
        <CardContent>
          <AdminDataTable
            config={menuTableConfig}
            dataSource={menus || []}
            loading={menusLoading}
            onRefresh={refreshMenus}
            onSearch={(search) => setMenuParams({ search })}
            searchValue={menuParams.search || ""}
          />
        </CardContent>
      </Card>

      {/* Create Menu Dialog */}
      <CreateMenuDialog
        isOpen={isCreateMenuOpen}
        onClose={() => setIsCreateMenuOpen(false)}
        onSuccess={() => {
          refreshMenus();
          setIsCreateMenuOpen(false);
        }}
      />

      {/* Edit Menu Dialog */}
      {editingMenu && (
        <EditMenuDialog
          menu={editingMenu}
          isOpen={!!editingMenu}
          onClose={() => setEditingMenu(null)}
          onSuccess={() => {
            refreshMenus();
            setEditingMenu(null);
          }}
        />
      )}

      {/* Menu Detail View */}
      {selectedMenu && (
        <MenuDetailView
          menu={selectedMenu}
          isOpen={!!selectedMenu}
          onClose={() => setSelectedMenu(null)}
          onRefresh={refreshMenus}
        />
      )}
    </div>
  );
}

// Create Menu Dialog Component
function CreateMenuDialog({
  isOpen,
  onClose,
  onSuccess,
}: {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}) {
  const [formData, setFormData] = useState({
    name: "",
    location: "",
    description: "",
    isActive: true,
  });
  const [loading, setLoading] = useState(false);
  const { create: createMenu } = useAdminCrud("/api/admin/menus");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name || !formData.location) {
      toast.error("Vui lòng điền đầy đủ thông tin bắt buộc");
      return;
    }

    setLoading(true);
    try {
      const result = await createMenu(formData);
      if (result) {
        toast.success("Tạo menu thành công");
        onSuccess();
        setFormData({
          name: "",
          location: "",
          description: "",
          isActive: true,
        });
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Tạo Menu Mới</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="name">Tên Menu *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) =>
                setFormData({ ...formData, name: e.target.value })
              }
              placeholder="Nhập tên menu..."
              required
            />
          </div>
          <div>
            <Label htmlFor="location">Vị trí *</Label>
            <Select
              value={formData.location}
              onValueChange={(value) =>
                setFormData({ ...formData, location: value })
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Chọn vị trí menu..." />
              </SelectTrigger>
              <SelectContent>
                {menuLocations.map((location) => (
                  <SelectItem key={location.value} value={location.value}>
                    {location.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="description">Mô tả</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) =>
                setFormData({ ...formData, description: e.target.value })
              }
              placeholder="Mô tả menu..."
              rows={3}
            />
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="isActive"
              checked={formData.isActive}
              onCheckedChange={(checked) =>
                setFormData({ ...formData, isActive: checked })
              }
            />
            <Label htmlFor="isActive">Kích hoạt menu</Label>
          </div>
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Hủy
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? "Đang tạo..." : "Tạo Menu"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}

// Edit Menu Dialog Component
function EditMenuDialog({
  menu,
  isOpen,
  onClose,
  onSuccess,
}: {
  menu: Menu;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}) {
  // Component implementation will be added in next edit
  return null;
}

// Menu Detail View Component
function MenuDetailView({
  menu,
  isOpen,
  onClose,
  onRefresh,
}: {
  menu: Menu;
  isOpen: boolean;
  onClose: () => void;
  onRefresh: () => void;
}) {
  // Component implementation will be added in next edit
  return null;
}
